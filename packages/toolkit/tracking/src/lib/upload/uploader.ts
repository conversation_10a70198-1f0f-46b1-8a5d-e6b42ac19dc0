import { CLOC_API_URL } from '../constant';
import { IClocConfig } from '../types';

export const createUploader = (config: IClocConfig) => {
	return async (data: string) => {
		const { tenantId, organizationId, token: bearer_token } = config;

		try {
			const headers: HeadersInit = {
				'Content-Type': 'application/json',
				Accept: 'application/json'
			};

			if (bearer_token) {
				headers['authorization'] = `Bearer ${bearer_token}`;
			}

			if (tenantId) {
				headers['tenant-id'] = tenantId;
			}

			if (organizationId) {
				headers['organization-id'] = organizationId;
			}

			const response = await fetch(CLOC_API_URL, {
				method: 'POST',
				headers,
				body: JSON.stringify({
					payload: data
				})
			});

			if (!response.ok) {
				throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
			}

			return await response.json();
		} catch (error) {
			console.error('Error uploading data:', error);
			throw error;
		}
	};
};
