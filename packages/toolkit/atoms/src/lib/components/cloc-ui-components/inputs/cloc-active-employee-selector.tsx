import { Select } from '@cloc/ui';
import { PermissionsEnum } from '@cloc/types';
import { useEffect, useState } from 'react';
import { useClocContext } from '@lib/context/cloc-context';
import { Users } from 'lucide-react';

export const ClocActiveEmployeeSelector = ({
	size,
	labeled = false,
	className
}: {
	size?: 'default' | 'sm' | 'lg' | null;
	labeled?: boolean;
	className?: string;
}) => {
	const {
		members,
		selectedEmployee,
		setSelectedEmployee,
		userPermissions,
		authenticatedUser: user,
		loadings: { membersLoading }
	} = useClocContext();

	const [selectMemberValues, setSelectMemberValues] = useState<{ label: string; value: string }[]>([]);

	const [isAllowedToChangeEmployee, setIsAllowedToChangeEmployee] = useState<boolean>(false);

	useEffect(() => {
		if (members && members.items[0]) {
			setSelectMemberValues(members.items.map((elt) => ({ label: elt.fullName, value: elt.id })));
		} else {
			setSelectMemberValues([]);
		}
	}, [members]);

	useEffect(() => {
		if (userPermissions) {
			const allowed = userPermissions.filter((elt) => elt.permission == PermissionsEnum.CHANGE_SELECTED_EMPLOYEE);
			if (allowed[0]) setIsAllowedToChangeEmployee(true);
		} else {
			setIsAllowedToChangeEmployee(false);
		}
	}, [userPermissions, user]);

	if (!isAllowedToChangeEmployee) {
		return null;
	}

	return (
		<div className=" flex flex-col gap-2 ">
			{labeled && (
				<label htmlFor="employee" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
					<Users className="inline h-4 w-4 mr-1" />
					Employee
				</label>
			)}

			<Select
				loading={membersLoading}
				name="employee"
				placeholder="Select employee"
				values={[{ label: 'All employees', value: 'all' }, ...selectMemberValues]}
				className={className}
				value={selectedEmployee}
				defaultValue={selectedEmployee}
				onValueChange={(value) => {
					setSelectedEmployee(value);
				}}
				size={size}
			/>
		</div>
	);
};
